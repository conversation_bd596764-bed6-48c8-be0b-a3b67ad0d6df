class Address:
    def __init__(self, street: str, city: str, house: str,
                 zip_code: str,
                 country: str = None):  # It's better to use zip_code instead of zip to avoid conflict with the built-in zip function
        self.street = street
        self.city = city
        self.country = country
        self.house = house
        self.zip_code = zip_code

    def __str__(self):
        return f"{self.house} {self.street}, {self.city}, {self.zip_code}, {self.country}"

    def __repr__(self):
        return f"{self.house} {self.street}, {self.city}, {self.zip_code}, {self.country}"


class Company:
    def __init__(self, name: str, owner: str, nip: str, regon: str, address: Address):
        self.name = name
        self.owner = owner
        self.regon = regon
        self.nip = nip  # NIP is a common tax identification number in some countries
        self.address = address  # Expecting an instance of Address

    def __str__(self):
        return f"{self.name} owned by {self.owner} located at {self.address}"

    def __repr__(self):
        return f"{self.name} owned by {self.owner} located at {self.address}"


class Hostel:
    def __init__(self, owner: str, passport: str, address: Address):
        self.owner = owner
        self.passport = passport
        self.address = address

    def __str__(self):
        return f"{self.owner} with passport {self.passport} located at {self.address}"

    def __repr__(self):
        return f"{self.owner} with passport {self.passport} located at {self.address}"


class Partner:
    def __init__(self, name: str, owner: str, address: Address):
        self.name = name
        self.owner = owner
        self.address = address

    def __str__(self):
        return f"{self.name} owned by {self.owner} located at {self.address}"

    def __repr__(self):
        return f"{self.name} owned by {self.owner} located at {self.address}"


class Worker:
    def __init__(self, worker_id: int, status: str, document_type: str, surname: str, name: str, sex: str,
                 legalization: str,
                 citizenship: str, pesel: str, passport: str, start_date: str, end_date: str,
                 doc_number: str, company: Company, partner: Partner, hostel: Hostel, work_type: str, pkd_code: str,
                 work_description: str, work_time: str, salary: str):
        self.worker_id = worker_id
        self.status = status
        self.document_type = document_type
        self.surname = surname
        self.name = name
        self.sex = sex
        self.legalization = legalization
        self.citizenship = citizenship
        self.pesel = pesel
        self.passport = passport
        self.start_date = start_date
        self.end_date = end_date
        self.doc_number = doc_number
        self.company = company
        self.partner = partner
        self.hostel = hostel
        self.work_type = work_type
        self.pkd_code = pkd_code
        self.work_description = work_description
        self.work_time = work_time
        self.salary = salary

    def __str__(self):
        return f'{self.worker_id} {self.status} {self.document_type} {self.surname} {self.name} {self.sex} ' \
               f'{self.citizenship} {self.pesel} {self.passport} {self.start_date} {self.end_date} {self.doc_number} ' \
               f'{self.company} {self.partner} {self.hostel} {self.work_type} {self.pkd_code} {self.work_description} ' \
               f'{self.work_time} {self.salary}'

    def __repr__(self):
        return f'{self.worker_id} {self.status} {self.document_type} {self.surname} {self.name} {self.sex}' \
               f'{self.citizenship} {self.pesel} {self.passport} {self.start_date} {self.end_date} {self.doc_number}' \
               f'{self.company} {self.partner} {self.hostel} {self.work_type} {self.pkd_code} {self.work_description}' \
               f'{self.work_time} {self.salary}'
