import os
from dotenv import load_dotenv
from google.oauth2.service_account import Credentials

load_dotenv()

SCOPES = ['https://www.googleapis.com/auth/spreadsheets', 'https://www.googleapis.com/auth/drive']
SERVICE_ACCOUNT_FILE = 'credentials/google-api.json'

credentials = Credentials.from_service_account_file(
    SERVICE_ACCOUNT_FILE, scopes=SCOPES)

general_sheet_id = os.getenv('GENERAL_SHEET_ID')
companies_sheet_id = os.getenv('COMPANIES_SHEET_ID')
partners_sheet_id = os.getenv('PARTNERS_SHEET_ID')
hostels_sheet_id = os.getenv('HOSTELS_SHEET_ID')
