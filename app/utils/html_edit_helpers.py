from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta


def get_doc_number_uslug(worker):
    # Assuming worker.start_date is a string like '23.04.2024'
    start_date = datetime.strptime(worker.start_date, '%d.%m.%Y')
    # Format the datetime object to get month and year as 'MM.YYYY'
    month_year = start_date.strftime('%m.%Y')
    return f'{worker.doc_number}/{month_year}'


def get_doc_number_polecenie_wyjazdu(worker, data):
    # Assuming worker.start_date is a string like '23.04.2024'
    start_date = datetime.strptime(data, '%d.%m.%Y')
    # Format the datetime object to get month and year as 'MM.YYYY'
    month = start_date.strftime('%m')
    return f'{worker.doc_number}/{month}'


def add_two_days(date_str):
    # Parse the date string into a datetime object
    date_format = '%d.%m.%Y'
    date_obj = datetime.strptime(date_str, date_format)

    # Add two days to the date
    new_date_obj = date_obj + timedelta(days=2)

    # Convert the datetime object back into a string
    new_date_str = new_date_obj.strftime(date_format)
    return new_date_str


def get_m_y_from_date(date_str):
    # Parse the date string into a datetime object
    date_format = '%d.%m.%Y'
    date_obj = datetime.strptime(date_str, date_format)

    # Format the datetime object to get month and year as 'MM.YYYY'
    month_year = date_obj.strftime('%m.%Y')
    return month_year


def subtract_one_day(date_str):
    # Parse the date string into a datetime object
    date_format = '%d.%m.%Y'
    date_obj = datetime.strptime(date_str, date_format)

    # Subtract one day from the date
    new_date_obj = date_obj - timedelta(days=1)

    # Convert the datetime object back into a string
    new_date_str = new_date_obj.strftime(date_format)
    return new_date_str


def add_three_months(date_str):
    # Parse the date string into a datetime object
    date_format = '%d.%m.%Y'
    date_obj = datetime.strptime(date_str, date_format)

    # Add three months to the date
    new_date_obj = date_obj + relativedelta(months=3)

    # Convert the datetime object back into a string
    new_date_str = new_date_obj.strftime(date_format)
    return new_date_str


def get_letter_based_on_pkd_code(pkd_code):
    # Extract the first two digits
    category_code = int(pkd_code[:2])

    # Determine the category and corresponding checkbox id
    if category_code in range(1, 4):
        return 'letter-a'
    elif category_code in range(5, 10):
        return 'letter-b'
    elif category_code in range(10, 34):
        return 'letter-c'
    elif category_code == 35:
        return 'letter-d'
    elif category_code in range(36, 40):
        return 'letter-e'
    elif category_code in range(41, 44):
        return 'letter-f'
    elif category_code in range(45, 48):
        return 'letter-g'
    elif category_code in range(49, 54):
        return 'letter-h'
    elif category_code in range(55, 57):
        return 'letter-i'
    elif category_code in range(58, 64):
        return 'letter-j'
    elif category_code in range(64, 67):
        return 'letter-k'
    elif category_code == 68:
        return 'letter-l'
    elif category_code in range(69, 76):
        return 'letter-m'
    elif category_code in range(77, 83):
        return 'letter-n'
    elif category_code == 84:
        return 'letter-o'
    elif category_code == 85:
        return 'letter-p'
    elif category_code in range(86, 89):
        return 'letter-q'
    elif category_code in range(90, 94):
        return 'letter-r'
    elif category_code in range(94, 97):
        return 'letter-s'
    elif category_code in range(97, 99):
        return 'letter-t'
    elif category_code == 99:
        return 'letter-u'
    else:
        return 'letter-f'


def is_worker_from_eu_country(worker):
    if worker.citizenship.lower() == 'RUMUŃSKIE'.lower():
        return 'Rumunia'
    elif worker.citizenship.lower() == 'Bułgarskie'.lower():
        return 'Bułgaria'
    else:
        return False


def generate_dates_harmonogram(start_date, end_date):
    # Перевірка на правильний формат дат
    try:
        start_month, start_year = start_date.split('.')
        end_month, end_year = end_date.split('.')
        start_month = int(start_month)
        start_year = int(start_year)
        end_month = int(end_month)
        end_year = int(end_year)
    except ValueError:
        return []

    # Створення масиву дат
    dates = []

    while (start_year, start_month) <= (end_year, end_month):
        # Перетворення числа місяця на римську цифру
        roman_month = {
            1: 'I', 2: 'II', 3: 'III', 4: 'IV', 5: 'V', 6: 'VI',
            7: 'VII', 8: 'VIII', 9: 'IX', 10: 'X', 11: 'XI', 12: 'XII'
        }[start_month]

        # Додавання дати до масиву
        dates.append(f"{roman_month}.{start_year}")

        # Збільшення місяця та року
        start_month += 1
        if start_month > 12:
            start_month = 1
            start_year += 1

    return dates


def get_full_address(worker):
    return f'{worker.hostel.address.street} {worker.hostel.address.house}, {worker.hostel.address.zip_code} {worker.hostel.address.city}'


def get_company_full_address(worker):
    return f'{worker.company.address.street} {worker.company.address.house}, {worker.company.address.zip_code} {worker.company.address.city}'


def get_partner_full_address(worker):
    return f'{worker.partner.address.street} {worker.partner.address.house}, {worker.partner.address.zip_code} {worker.partner.address.city}'


def add_one_year(date_str):
    # Parse the date string into a datetime object
    date_format = '%d.%m.%Y'
    date_obj = datetime.strptime(date_str, date_format)

    # Add one year to the date
    new_date_obj = date_obj + relativedelta(years=1)

    # Convert the datetime object back into a string
    new_date_str = new_date_obj.strftime(date_format)
    return new_date_str
