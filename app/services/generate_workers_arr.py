from app.models.entities import Worker, Company, Partner, Hostel, Address
from app.config.settings import *
from app.utils.normalization import normalize_name
from app.utils.address_creation import create_address
from app.services.sheets_api import get_data_from_sheet
from app.services.data_transformation import create_dicts
import app.constants.sheet_names as sheet_names


def generate_workers_arr():
    # Pre-load all data from sheets and create dictionaries
    general_data_dicts = create_dicts(get_data_from_sheet(general_sheet_id,
                                                          sheet_names.SHEET_NAMES))
    companies_data_dicts = {normalize_name(company.get('Назва', '')): company for company in
                            create_dicts(get_data_from_sheet(companies_sheet_id))}
    partners_data_dicts = {normalize_name(partner.get('Партнер', '')): partner for partner in
                           create_dicts(get_data_from_sheet(partners_sheet_id, ['Аркуш1']))}
    hostels_data_dicts = {normalize_name(hostel.get('Власник', '')): hostel for hostel in
                          create_dicts(get_data_from_sheet(hostels_sheet_id))}

    # Prepare to collect worker data
    workers = []

    # Filter out dictionaries where 'Зроблено' is not 'FALSE'
    filtered_general_data = [data for data in general_data_dicts if data.get('Зроблено') == 'FALSE']

    for data_dict in filtered_general_data:
        company_name = normalize_name(data_dict.get('Наша компанія', ''))
        partner_name = normalize_name(data_dict.get('Компанія делегування', ''))
        hostel_name = normalize_name(data_dict.get('Власник орендного житла', ''))

        # Find company, partner, and hostel data using preprocessed dictionaries
        company_data = companies_data_dicts.get(company_name)
        if company_data:
            address = create_address(company_data)
            company_data = Company(
                company_data.get('Назва'),
                company_data.get('Власник'),
                company_data.get('NIP'),
                company_data.get('REGON'),
                address
            )

        partner_data = partners_data_dicts.get(partner_name)
        if partner_data:
            address = create_address(partner_data)
            partner_data = Partner(
                partner_data.get('Партнер'),
                partner_data.get('Представник'),
                address
            )

        hostel_data = hostels_data_dicts.get(hostel_name)
        if hostel_data:
            address = create_address(hostel_data)
            hostel_data = Hostel(
                hostel_data.get('Власник'),
                hostel_data.get('Номер паспорту'),
                address
            )

        # Create worker object with all necessary data

        try:
            worker_data = Worker(
                data_dict.get('ID'), data_dict.get('Зроблено'), data_dict.get('Тип документу'),
                data_dict.get('Прізвище'), data_dict.get('Ім`я'),
                data_dict.get('Стать'), data_dict.get('Легалізація'), data_dict.get('Країна народження'),
                f'{data_dict.get('Номер PESEL')}'.strip().replace('`', ''),
                data_dict.get('Номер паспорту').strip().replace('`', ''),
                data_dict.get('Дата початку праці').strip(), data_dict.get('Дата кінця праці').strip(),
                data_dict.get('Номер документу'),
                company_data, partner_data, hostel_data, data_dict.get('Тип роботи'),
                data_dict.get('PKD Код (3 цифри)'),
                data_dict.get('Опис праці'), data_dict.get('Ставка'), data_dict.get('Зарплата')
            )
            workers.append(worker_data)
        except Exception as e:
            # Выводим фамилию и имя работника, у которого возникла ошибка
            print(f"Ошибка при обработке работника: {data_dict.get('Прізвище')} {data_dict.get('Ім`я')}")
            print(f"Ошибка: {str(e)}")

    return workers
