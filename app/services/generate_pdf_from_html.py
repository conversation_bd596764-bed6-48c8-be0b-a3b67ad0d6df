from pyppeteer import launch


async def generate_pdf_from_html(file_path, pdf_path):
    # Launch the browser
    browser = await launch()
    page = await browser.newPage()

    # Read HTML content from the file
    with open(file_path, 'r', encoding='utf-8') as file:
        html_content = file.read()

    # Set the HTML content
    await page.setContent(html_content)

    # Generate PDF
    await page.pdf({'path': pdf_path, 'format': 'A4', 'scale': 1})

    # Close the browser
    await browser.close()
