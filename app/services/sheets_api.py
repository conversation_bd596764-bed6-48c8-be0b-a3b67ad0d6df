from googleapiclient.discovery import build
from app.config.settings import credentials


def get_data_from_sheet(sheet_id: str, custom_ranges=None):
    if custom_ranges is None:
        custom_ranges = ['Sheet1']

    service = build('sheets', 'v4', credentials=credentials)
    sheet = service.spreadsheets()

    data = []
    for custom_range in custom_ranges:
        result = sheet.values().get(spreadsheetId=sheet_id, range=custom_range).execute()
        data.extend(result.get('values', []))

    return data


def update_done_status(sheet_id: str, worker_id: str, worker_id_col: int, done_col: int, sheet_names=None):
    if sheet_names is None:
        sheet_names = ['Sheet1']

    service = build('sheets', 'v4', credentials=credentials)
    sheet = service.spreadsheets()

    # Store all update results if needed
    update_results = []

    for sheet_name in sheet_names:
        # Fetch the current data from the sheet to find the worker's row
        result = sheet.values().get(spreadsheetId=sheet_id, range=f"{sheet_name}").execute()
        data = result.get('values', [])

        # Find the row where the worker's ID matches
        for row_index, row in enumerate(data):
            if len(row) > worker_id_col and row[worker_id_col] == worker_id:
                # Calculate the A1 notation of the cell to update
                cell_to_update = f"{sheet_name}!{chr(65 + done_col)}{row_index + 1}"
                # Prepare the value to set ('TRUE')
                values = [["TRUE"]]
                body = {'values': values}
                # Update the cell and store the result
                update_result = sheet.values().update(
                    spreadsheetId=sheet_id, range=cell_to_update,
                    valueInputOption='USER_ENTERED', body=body).execute()
                update_results.append(update_result)  # Collect results for possible further actions

    # Optionally return the update results or None if no result needed
    return update_results if update_results else None
