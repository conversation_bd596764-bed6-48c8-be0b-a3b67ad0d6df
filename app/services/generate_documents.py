from app.services.generate_workers_arr import generate_workers_arr
from app.services.generate_pdfs import generate_umowa_uslug, generate_polecenie_wyjazdu, generate_umowa_najmu_lokalu, \
    generate_umowa_pracy, generate_zusatz_zum, generate_zus_us_4, generate_zus_us_54, generate_zus_us_55, \
    generate_harmonogram
import os
from app.models.entities import Worker
from app.services.sheets_api import update_done_status
from app.config.settings import general_sheet_id
from app.utils.html_edit_helpers import is_worker_from_eu_country
from app.constants.sheet_names import SHEET_NAMES
from telegram import Update
import zipfile
from io import BytesIO
import shutil


def check_worker_fields(worker: Worker):
    for attr_name in vars(worker):
        # Отримуємо значення кожного атрибуту
        attr_value = getattr(worker, attr_name)

        # Перевірка на пусті значення або None для всіх атрибутів, крім 'pesel' та 'legalization'
        if attr_value is None or attr_value == "":
            if attr_name not in ['pesel', 'legalization']:
                raise ValueError(f"Attribute {attr_name} in {worker.name + ' ' + worker.surname} is None or empty.")

        # Додаткова умова для працівників не з європи і 'legalization'
        if not is_worker_from_eu_country(worker) and attr_name == 'legalization':
            if attr_value is None or attr_value == "":
                raise ValueError(
                    f"Attribute {attr_name} in {worker.name + ' ' + worker.surname} is required for workers from "
                    f"non-EU countries and is None or empty.")


# TODO optimize this function
def check_if_worker_from_eu(worker: Worker):
    if worker.citizenship.lower().replace(" ", "") == "RUMUŃSKIE".lower():
        return True
    elif worker.citizenship.lower().replace(" ", "") == "Bułgarskie".lower():
        return True
    else:
        return False


async def generate_us_54_or_us_55(worker: Worker, worker_path):
    if check_if_worker_from_eu(worker):
        await generate_zus_us_55(worker, worker_path)
    else:
        await generate_zus_us_54(worker, worker_path)


async def umowa_pracy_docs(worker: Worker, worker_path):
    await generate_umowa_pracy(worker, worker_path)
    await generate_harmonogram(worker, worker_path)
    await generate_polecenie_wyjazdu(worker, worker_path)
    await generate_us_54_or_us_55(worker, worker_path)


async def umowa_uslug_docs(worker: Worker, worker_path):
    await generate_umowa_uslug(worker, worker_path)
    await generate_us_54_or_us_55(worker, worker_path)
    await generate_zusatz_zum(worker, worker_path)


async def generate_documents(update: Update):
    workers = generate_workers_arr()
    for worker in workers:
        try:
            worker_path = f'documents/{worker.name}_{worker.surname}_{worker.document_type}'
            os.makedirs(worker_path, exist_ok=True)
            await generate_umowa_najmu_lokalu(worker, worker_path)
            await generate_zus_us_4(worker, worker_path)
            if worker.document_type == 'Umowa o pracę':
                await umowa_pracy_docs(worker, worker_path)
            else:
                await umowa_uslug_docs(worker, worker_path)
        except Exception as e:
            await update.message.reply_text(
                f'Помилка в генерації документу для {worker.name} {worker.surname}: {str(e)}')
            continue

        update_done_status(general_sheet_id, worker.worker_id, 0, 1, sheet_names=SHEET_NAMES)

        # After generating all documents, zip them
        zip_buffer = BytesIO()
        with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:

            for root, dirs, files in os.walk(worker_path):
                for file in files:
                    zip_file.write(os.path.join(root, file),
                                   os.path.relpath(os.path.join(root, file), os.path.join(worker_path, '..')))

        zip_buffer.seek(0)

        await update.message.reply_document(zip_buffer, filename=f'{worker.name}_{worker.surname}.zip')

        # Clean up the generated documents
        shutil.rmtree(worker_path)  # Removes the directory and all its contents

    return None
