import os
import random

from bs4 import BeautifulSoup
from app.constants.file_paths import *
from app.services.generate_pdf_from_html import generate_pdf_from_html
from app.models.entities import Worker
import aiofiles
from app.utils.html_edit_helpers import *


async def read_html_template(path):
    async with aiofiles.open(path, 'r', encoding='utf-8') as file:
        return await file.read()


async def process_document(worker, worker_dir, template_path, html_filename, pdf_filename, updates):
    html_content = await read_html_template(template_path)
    soup = BeautifulSoup(html_content, 'html.parser')

    html_path = f'{worker_dir}/{html_filename}'
    pdf_path = f'{worker_dir}/{pdf_filename}'

    # Use a general purpose function to edit HTML and save to PDF
    await edit_html_save_pdf(soup, html_path, pdf_path, updates)


async def edit_html_save_pdf(soup, html_path, pdf_path, updates):
    # Apply updates to the soup
    for data_property, new_value in updates.items():
        for element in soup.find_all(attrs={"data-property": data_property}):
            if 'class' in element.attrs and 'text-input-group' in element['class']:
                all_children = element.find_all('div')
                try:
                    for index, letter in enumerate(new_value.replace('.', '')):
                        all_children[index].string = letter
                except IndexError:
                    raise IndexError(f'Error in data property: {data_property}')
            else:
                element.string = new_value

    # Write the modified HTML back to a file
    async with aiofiles.open(html_path, 'w', encoding='utf-8') as file:
        await file.write(str(soup))

    await generate_pdf_from_html(html_path, pdf_path)

    os.remove(html_path)


async def generate_umowa_uslug(worker: Worker, worker_dir):
    updates = {
        "doc_number": get_doc_number_uslug(worker),
        "date_start": worker.start_date,
        "date_end": worker.end_date,
        "company_city": worker.company.address.city,
        "company_name": f'{worker.company.name} Sp z O.O.',
        "company_nip": worker.company.nip,
        "company_owner": worker.company.owner,
        "full_name": f'{worker.name} {worker.surname}',
        "passport": worker.passport,
        "full_address": get_full_address(worker),
        "work_description": worker.work_description,
        "company_full_address": get_company_full_address(worker),
    }
    await process_document(worker, worker_dir, UMOWA_USLUG_PATH, 'umowa_uslug.html', 'umowa_uslug.pdf', updates)


async def generate_polecenie_wyjazdu(worker: Worker, worker_dir):
    date_start_plus_two_days = add_two_days(worker.start_date)
    full_name = f'{worker.name} {worker.surname}'
    date_end = add_three_months(date_start_plus_two_days)
    updates = {
        "doc_number": get_doc_number_polecenie_wyjazdu(worker, date_start_plus_two_days),
        "date_start_plus_two_days": date_start_plus_two_days,
        "full_name": full_name,
        "date_start": date_start_plus_two_days,
        "delegation_company_name": worker.partner.name,
        "date_end": date_end,
        "delegation_company_country": worker.partner.address.country,
        "delegation_full_address": get_partner_full_address(worker),
        "company_city": worker.company.address.city,
        "delegation_city": worker.partner.address.city,
        "company_name": worker.company.name
    }
    await process_document(worker, worker_dir, POLECENIE_WYJAZDU_PATH,
                           'polecenie-wyjazdu-sluzbowego-i-rachunek-kosztow-podrozy.html',
                           'polecenie-wyjazdu-sluzbowego.pdf', updates)


async def generate_umowa_najmu_lokalu(worker: Worker, worker_dir):
    date_start = subtract_one_day(worker.start_date)
    full_name = f'{worker.name} {worker.surname}'
    updates = {
        "date_start_day_before": date_start,
        "company_city": worker.company.address.city,
        "house_owner": worker.hostel.owner,
        "house_full_address": get_full_address(worker),
        "house_owner_passport": worker.hostel.passport,
        "full_name": full_name,
        "passport": worker.passport,
        "one_year_date_start": add_one_year(date_start),
    }
    await process_document(worker, worker_dir, UMOWA_NAJMU_LOKALU_PATH, 'umowa_najmu_lokalu.html',
                           'umowa_najmu_lokalu.pdf', updates)


async def generate_umowa_pracy(worker: Worker, worker_dir):
    full_name = f'{worker.name} {worker.surname}'
    updates = {
        "date_start": worker.start_date,
        "date_end": worker.end_date,
        "company_name": f'{worker.company.name.upper()} SPÓŁKA Z OGRANICZONĄ ODPOWIEDZIALNOŚCIĄ',
        "company_full_address": f'{get_company_full_address(worker)}',
        "full_name": full_name,
        "full_address": get_full_address(worker),
        "work_type": worker.work_type,
        "company_street_house": f'{worker.company.address.street} {worker.company.address.house}, {worker.company.address.zip_code}, {worker.company.address.city} oraz teren {worker.partner.address.country} według zleceń',
        "work_time": worker.work_time,
        "salary": worker.salary,
        "city_of_sign": worker.hostel.address.city,
    }

    await process_document(worker, worker_dir, UMOWA_PRACY_GW_PATH, 'umowa_pracy.html',
                           'umowa_pracy.pdf', updates)


async def generate_zusatz_zum(worker: Worker, worker_dir):
    full_name = f'{worker.name} {worker.surname}'
    updates = {
        "date_start": worker.start_date,
        "date_end": worker.end_date,
        "company_name": worker.company.name.upper(),
        "company_full_address": get_company_full_address(worker),
        "full_name": full_name,
        "passport": worker.passport,
        "full_address": get_full_address(worker),
        "delegation_company_name": worker.partner.name,
        "delegation_full_address": get_partner_full_address(worker),
        "delegation_company_owner": worker.partner.owner,
    }

    await process_document(worker, worker_dir, ZUSTAZ_ZUM_PATH, 'zusatz_zum.html', 'zusatz_zum.pdf', updates)


def is_partner_polish(partner_country: str):
    return partner_country.lower().replace(" ", "") == 'polska'


def get_partner_country_key(partner_country: str):
    if partner_country.lower().strip() == 'Francja'.lower():
        return 'fr'
    elif partner_country.lower().strip() == 'Niemcy'.lower():
        return 'de'
    elif partner_country.lower().strip() == 'Belgia'.lower():
        return 'be'
    elif partner_country.lower().strip() == 'Francja'.lower():
        return 'fr'
    elif partner_country.lower().strip() == 'Holandia'.lower():
        return 'nl'
    elif partner_country.lower().strip() == 'Czechy'.lower():
        return 'cz'
    elif partner_country.lower().strip() == 'Austria'.lower():
        return 'at'
    else:
        return 'de'


async def generate_zus_us_4(worker: Worker, worker_dir):
    letter = get_letter_based_on_pkd_code(worker.pkd_code)
    updates = {
        "pesel": worker.pesel,
        "passport": f'PASZPORT: {worker.passport}',
        "surname": worker.surname,
        "name": worker.name,
        "nationalization": worker.citizenship,
        "street": worker.hostel.address.street,
        "house": worker.hostel.address.house,
        "postal_code": worker.hostel.address.zip_code,
        "city": worker.hostel.address.city,
        "country": worker.hostel.address.country,
        "nip": worker.company.nip,
        "regon": worker.company.regon,
        "company_name": f'{worker.company.name.upper()} SP. Z O.O.',
        "company_street": worker.company.address.street,
        "company_house": worker.company.address.house,
        "company_postal_code": worker.company.address.zip_code,
        "company_city": worker.company.address.city,
        "company_country": worker.company.address.country,
        "pkd": worker.pkd_code,
        letter: 'X',
        "delegation_company_name": worker.partner.name,
        "delegation_company_street": worker.partner.address.street,
        "delegation_company_house": worker.partner.address.house,
        "delegation_company_postal_code": worker.partner.address.zip_code,
        "delegation_company_city": worker.partner.address.city,
        "delegation_company_country": worker.partner.address.country,
        "date_start": worker.start_date,
        "date_end": worker.end_date,
        "pl_time_procent": '60',
        "pl_payment_procent": '60',
        f'{get_partner_country_key(worker.partner.address.country)}_time_procent': '40',
        f'{get_partner_country_key(worker.partner.address.country)}_payment_procent': '40',
    }

    partner_key = 'polish_partner' if is_partner_polish(worker.partner.address.country) else 'other_country_partner'
    updates[partner_key] = 'X'

    await process_document(worker, worker_dir, ZUS_US_4_PATH, 'zus-us-4.html', 'zus-us-4.pdf', updates)
    return


def get_legalization_document(document):
    if document.lower() == 'Паспорт ЄС'.lower():
        return 'eu_citizenship'
    elif document.lower() == 'Карта побита'.lower():
        return 'karta_pobytu'
    elif document.lower() == 'Віза'.lower():
        return 'visa'


async def generate_zus_us_54(worker: Worker, worker_dir):
    updates = {
        "surname": worker.surname,
        "name": worker.name,
        "pesel": worker.pesel,
        "passport": f'PASZPORT: {worker.passport}',
        "nationalization": worker.citizenship,
        "street": worker.hostel.address.street,
        "house": worker.hostel.address.house,
        "postal_code": worker.hostel.address.zip_code,
        "city": worker.hostel.address.city,
        "country": worker.hostel.address.country,
        "nip": worker.company.nip,
        "regon": worker.company.regon,
        "company_name": f'{worker.company.name.upper()} SP. Z O.O.',
        "company_street": worker.company.address.street,
        "company_house": worker.company.address.house,
        "company_postal_code": worker.company.address.zip_code,
        "company_city": worker.company.address.city,
        "company_country": worker.company.address.country,
        "pkd": worker.pkd_code,
        "work_type": worker.work_type,
        "date_start": worker.start_date,
        "date_end": worker.end_date,
        "company_full_address": get_company_full_address(worker),
        "full_address": get_full_address(worker),
        "work_description": worker.work_description,
        get_legalization_document(worker.legalization): 'X',
    }
    await process_document(worker, worker_dir, ZUS_US_54_PATH, 'zus-us-54.html', 'zus-us-54.pdf', updates)
    return


async def generate_zus_us_55(worker, worker_dir):
    updates = {
        "surname": worker.surname,
        "name": worker.name,
        "pesel": worker.pesel,
        "passport": f'PASZPORT: {worker.passport}',
        "nationalization": worker.citizenship,
        "street": worker.hostel.address.street,
        "house": worker.hostel.address.house,
        "postal_code": worker.hostel.address.zip_code,
        "city": worker.hostel.address.city,
        "country": worker.hostel.address.country,
        "nip": worker.company.nip,
        "regon": worker.company.regon,
        "company_name": f'{worker.company.name.upper()} SP. Z O.O.',
        "company_street": worker.company.address.street,
        "company_house": worker.company.address.house,
        "company_postal_code": worker.company.address.zip_code,
        "company_city": worker.company.address.city,
        "company_country": worker.company.address.country,
        "pkd": worker.pkd_code,
        "work_type": worker.work_type,
        "date_start": worker.start_date,
        "date_end": worker.end_date,
        "company_full_address": get_company_full_address(worker),
        "full_address": get_full_address(worker),
        "work_description": worker.work_description,
    }

    if is_worker_from_eu_country(worker):
        native_country = is_worker_from_eu_country(worker)
        updates['native_country'] = native_country
    else:
        updates['w_polsce'] = 'X'

    await process_document(worker, worker_dir, ZUS_US_55_PATH, 'zus-us-55.html', 'zus-us-55.pdf', updates)
    return


async def generate_harmonogram(worker, worker_dir):
    start_date_m_y = get_m_y_from_date(worker.start_date)
    end_date_m_y = get_m_y_from_date(worker.end_date)

    procents = {
        '1': {
            'our': 75,
            'partner': 25,
        },
        '2': {
            'our': 60,
            'partner': 40,
        },
        '3': {
            'our': 80,
            'partner': 20,
        },
        '4': {
            'our': 70,
            'partner': 30,
        },
        '5': {
            'our': 65,
            'partner': 35,
        }
    }

    general_hours = {
        '1': [160, 158, 168, 156, 168, 160, 160, 130, 180, 152, 160],
        '1/2': [80, 79, 84, 78, 84, 80, 80, 65, 90, 76, 80],
        '1/4': [40, 39, 42, 39, 42, 40, 40, 32, 45, 38, 40],
        '1/8': [20, 20, 21, 20, 21, 20, 20, 16, 23, 19, 20],
    }

    updates = {
        "date_start": worker.start_date,
        "date_start_m-y": start_date_m_y,
        "date_end_m-y": end_date_m_y,
        "company_name": worker.company.name,
        "company_street_number": f'{worker.company.address.street} {worker.company.address.house}',
        "company_postal_code": worker.company.address.zip_code,
        "company_city": worker.company.address.city,
        "company_regon": worker.company.regon,
        "company_nip": worker.company.nip,
        "name": worker.name,
        "surname": worker.surname,
        "company_owner": worker.company.owner,
        "country": worker.partner.address.country,
    }
    # Add dynamic row updates
    if worker.work_time == '1/2':
        work_time = '1/2'
    elif worker.work_time == '1/8':
        work_time = '1/8'
    elif worker.work_time == '1/4':
        work_time = '1/4'
    else:
        work_time = '1'

    # get random number from 1 to 5
    random_number = str(random.randint(1, 5))

    for i, hours in enumerate(general_hours[work_time], start=1):
        updates[f'our_row_{i}'] = str(hours * procents[random_number]['our'] / 100)
        updates[f'partner_row_{i}'] = str(hours * procents[random_number]['partner'] / 100)
        updates[f'general_row_{i}'] = str(hours)

    dates = generate_dates_harmonogram(start_date_m_y, end_date_m_y)

    for index, date in enumerate(dates):
        updates[f'{index + 1}_input'] = date

    # if worker.work_time == '1/2':
    #     harmonogram = HARMONOGRAM_PATH_1_2
    # elif worker.work_time == '1/8':
    #     harmonogram = HARMONOGRAM_PATH_1_8
    # elif worker.work_time == '1/4':
    #     harmonogram = HARMONOGRAM_PATH_1_4
    # else:
    #     harmonogram = HARMONOGRAM_PATH_1

    await process_document(worker, worker_dir, HARMONOGRAM_PATH_1, 'rozliczeniowy_harmonogram.html',
                           'rozliczeniowy_harmonogram.pdf', updates)
    return
