<!DOCTYPE html>
<html lang="pl">

<head>
    <meta charset="UTF-8">
    <title>ZUS Form</title>
    <style>
        /*
            Common styles
        */

        :root {
            --color-border: grey;
        }

        .hr {
            unicode-bidi: isolate;
            overflow: hidden;
            border-style: solid;
            border-width: 1px;
            border-color: var(--color-border);
            border-top: none;
        }


        html {
            font-size: 12pt;
            font-style: normal;
            font-weight: normal;
            visibility: visible;
        }

        body {
            font-family: Arial, sans-serif;
            margin: 0;
        }

        .page {
            width: 793.626667pt;
            height: 1122.666667pt;
            padding: 60pt 90pt;
            box-sizing: border-box;
            position: relative;
            page-break-after: always;
            page-break-inside: avoid;
        }

        .t-center {
            text-align: center;
        }

        .t-left {
            text-align: left;
        }

        .t-right {
            text-align: right;
        }

        .columns {
            display: flex;
        }

        .columns .column {
            flex: 1;
        }

        .special_text {
            text-align: center;
            position: absolute;
            width: 100%;
            left: 0;
            bottom: 0;
            margin-bottom: 30pt;
        }

        .page_number_text {
            text-align: right;
            position: absolute;
            width: 100%;
            left: 0;
            bottom: 0;
            margin-bottom: 40pt;
            padding-right: 90pt;
            box-sizing: border-box;
        }

        /*
            Page1 styles
        */

        .page1 .form1 {
            width: 100%;
            display: flex;
            flex-wrap: nowrap;
            flex-direction: row;
            align-items: stretch;
        }

        .page1 .form1 .half {
            flex: 1;
            border: 1pt solid var(--color-border);
        }

        .page1 .form1 .half.section1 {
            margin-top: 106pt;
            position: relative;
            border-right: none;
        }

        .page1 .form1 .half.section1 .text1 {
            position: absolute;
            text-align: center;
            width: 100%;
            font-size: 9.35pt;
            margin-top: -16pt;
        }

        .page1 .form1 .half.section1 .title {
            font-size: 18.6pt;
            text-align: center;
            font-weight: 800;
            margin-top: 5pt;
            line-height: 27pt;
        }

        .page1 .form1 .half.section1 .text2 {
            text-align: center;
            font-size: 10.54pt;
            margin-top: 20pt;
        }

        .page1 .form1 .half.section1 .text3 {
            font-size: 10.54pt;
            margin: 26pt 20pt 0 20pt;
            line-height: 33pt;
            text-align: center;
        }

        .page1 .form1 .half.section1 .text4 {
            font-size: 9.35pt;
            text-align: center;
            position: absolute;
            width: 100%;
            margin-top: 50pt;
        }

        .page1 .form1 .half.section1 .text5,
        .page1 .form1 .half.section1 .text6 {
            font-size: 10.54pt;
            margin: 0 20pt;
            line-height: 30pt;
            text-align: center;
        }

        .page1 .form1 .half.section1 .text7 {
            font-size: 9.35pt;
            text-align: center;
            position: absolute;
            width: 100%;
            margin-top: 20pt;
        }

        .page1 .form1 .half.section1 .text8 {
            font-size: 10.54pt;
            padding: 3pt;
        }

        .page1 .form1 .half.section1 .text11 {
            display: flex;
        }

        .page1 .form1 .half.section1 .text9,
        .page1 .form1 .half.section1 .text10 {
            flex: 1;
            text-align: center;
        }

        .page1 .form1 .half.section2 .title {
            width: 100%;
            border-bottom: 1pt solid var(--color-border);
            padding: 5pt 0 5pt 36pt;
            box-sizing: border-box;
        }

        .page1 .line1 {
            margin-left: -30pt;
            margin-right: -30pt;
            border-bottom: 1pt dashed var(--color-border);
        }

        .page1 .line2 {
            border-bottom: 1pt dotted var(--color-border);
        }

        .page1 .text11 {
        }

        .page1 .text12 {
        }

        .page1 .text13 {
            text-align: right;
        }

        .page1 .tables {
        }

        .page1 .tables table {
            width: 100%;
            border-collapse: collapse;
        }

        .page1 .tables table td {
            border: 1pt solid var(--color-border);
            padding: 3pt;
            margin: 0pt;
            box-sizing: border-box;
            text-align: center;
        }

        .page1 .tables {
            display: flex;
        }

        .page1 .tables .table1 {
            flex: 1;
            padding-right: 10%;
            box-sizing: border-box;
            max-width: 50%;
        }

        .page1 .tables .table2 {
            flex: 1;
            box-sizing: border-box;
        }

        .page1 .text15 {
            display: flex;
            justify-content: flex-end;
        }

        .page1 .text16,
        .page1 .text17 {
            text-align: center;
            margin-left: 30pt;
        }

        /*
            Page2 styles
        */

        .page2 {
        }

        .page2 .title {
            font-size: 16pt;
            text-align: center;
            font-weight: 800;
        }


        .page2 .table table {
            width: 100%;
            border-collapse: collapse;
        }

        .page2 .table table td {
            border: 1pt solid var(--color-border);
            padding: 3pt;
            margin: 0pt;
            box-sizing: border-box;
            text-align: center;
        }

        .page2 .table table td.t-left {
            text-align: left;
        }

        .page2 .table table td.t-right {
            text-align: right;
        }

        /*
            Help styles
            remove if unnecessary
        */

        /*
                *[data-property] {
                    font-weight: 800;
                    color: red;
                }
        */
    </style>
</head>

<body>
<div class="page page1">
    <div class="form1">
        <div class="half section1">
            <div class="text1">Pieczątka wysyłającego</div>
            <div class="title">POLECENIE WYJAZDU SŁUŻBOWEGO Nr <span
                    data-property="doc_number">doc_number</span></div>
            <div class="text2">z dnia <span data-property="date_start_plus_two_days">date_start_plus_two_days</span>
            </div>
            <div class="text4">(imię i nazwisko)</div>
            <div class="text3">dla <span data-property="full_name">full_name</span></div>
            <div class="text7">(stanowisko służbowe)</div>
            <div class="text5">
                Wykonywanie służbowych obowiązków
                <br>
                do <span data-property="delegation_company_name">delegation_company_name</span>
                <br>
                <span data-property="delegation_full_address">delegation_full_address</span>
            </div>
            <div class="text6">
                na czas od <span data-property="date_start_plus_two_days">date_start_plus_two_days</span> do <span
                    data-property="date_end">date_end</span>
                <br>
                w celu wykonania służbowych obowiązków na terytorium
                <br>
                <span data-property="delegation_company_country">Niemcy</span> według Umów o podwykonawstwo pomiędzy
                <br>
                <span data-property="company_name"></span> a <span data-property="delegation_company_name">delegation_company_name</span>
                <br>
            </div>
            <div class="hr"></div>
            <div class="text8">
                środki lokomocji
                <br>
                <br>
                <br>
                <br>
            </div>
            <div class="hr"></div>
            <br>
            <br>
            <div class="text11">
                <div class="text9">
                    <span data-property="date_start">date_start</span>
                    <br>
                    <small>data</small>
                </div>
                <div class="text10">
                    . . . . . . . . . . . . . . . .
                    <br>
                    <small>podpis wysyłającego</small>
                </div>
            </div>
            <br>
        </div>
        <div class="half section2">
            <div class="title">STWIERDZENIE POBYTU SŁUŻBOWEGO</div>
        </div>
    </div>
    <br>
    <div class="line1"></div>
    <br>
    <div class="text11">
        Proszę o wypłacenie zaliczk i w kwocie 0.00 euro słownie 0.00 euro
    </div>
    <br>
    <div class="line2"></div>
    <br>
    <div class="text12">
        na pokrycie wydatków zgodnie z poleceniem wyjazdu służbowego nr <span
            data-property="doc_number">doc_number</span>
    </div>
    <br>
    <div class="text13">
        . . . . . . . . . . . . . . . . . .
        <br>
        <br>
        <small>Podpis delegowanego</small>
    </div>
    <br>
    <div class="text14">Zatwierdzono na 0.00 euro słownie 0.00 euro</div>
    <br>
    <div class="tables">
        <div class="table1">
            <table>
                <tr>
                    <td>Część</td>
                    <td>Dział</td>
                    <td>Rozdział</td>
                    <td width="50pt">§</td>
                    <td>Poz.</td>
                </tr>
                <tr>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                </tr>
            </table>
        </div>
        <div class="table2">
            <table>
                <tr>
                    <td colspan="2" width="66%">Konto</td>
                    <td rowspan="2">Nr dowodu</td>
                </tr>
                <tr>
                    <td>Wn</td>
                    <td>Ma</td>
                </tr>
                <tr>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                </tr>
            </table>
        </div>
    </div>
    <br>
    <div class="text15">
        <div class="text16">
            <span data-property="date_start_plus_two_days">date_start_plus_two_days</span>
            <br>
            <small>data</small>
        </div>
        <div class="text17">
            . . . . . . . . . . . . . . . .
            <br>
            <small>podpisy sprawdzających</small>
        </div>
    </div>
    <div class="special_text">
        <small>Źródło: Wydawnictwo Podatkowe GOFIN sp. z o.o. - www.Druki.Gofin.pl</small>
    </div>
    <div class="page_number_text">
        str. 1
    </div>
</div>
<div class="page page2">
    <div class="title">RACHUNEK KOSZTÓW PODRÓŻY</div>
    <br>
    <div class="table">
        <table>
            <tr>
                <td colspan="3" width="30%">W Y J A Z D</td>
                <td colspan="3" width="30%">P R Z Y J A Z D</td>
                <td rowspan="2" width="20%">Środki lokomocji*</td>
                <td width="20%">Koszty przejazdu</td>
            </tr>
            <tr>
                <td>miejscowość</td>
                <td>data</td>
                <td>godz.</td>
                <td>miejscowość</td>
                <td>data</td>
                <td>godz.</td>
                <td>zł i gr</td>
            </tr>
            <tr>
                <td><span data-property="company_city"></span></td>
                <td><span data-property="date_start_plus_two_days">test</span></td>
                <td>&nbsp;</td>
                <td><span data-property="delegation_city">delegation_city</span></td>
                <td><span data-property="date_start_plus_two_days">test</span></td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
            </tr>
            <tr>
                <td><span data-property="delegation_city">delegation_city</span></td>
                <td><span data-property="date_end">date_end</span></td>
                <td>&nbsp;</td>
                <td><span data-property="company_city"></span></td>
                <td><span data-property="date_end">date_end</span></td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
            </tr>
            <tr>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
            </tr>
            <tr>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
            </tr>
            <tr>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
            </tr>
            <tr>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
            </tr>
            <tr>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
            </tr>
            <tr>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
            </tr>
            <tr>
                <td colspan="4" rowspan="2">Rachunek sprawdzono pod względem</td>
                <td colspan="3" class="t-left">Ryczałty za dojazdy</td>
                <td>&nbsp;</td>
            </tr>
            <tr>
                <td colspan="3" class="t-left">Dojazdy udokumentowane</td>
                <td>&nbsp;</td>
            </tr>
            <tr>
                <td colspan="2" rowspan="3">Merytorycznym</td>
                <td colspan="2" rowspan="3">Formalnym i rachunkowym</td>
                <td colspan="3" class="t-left">Razem przejazdy, dojazdy</td>
                <td>&nbsp;</td>
            </tr>
            <tr>
                <td colspan="3" class="t-left">Diety</td>
                <td>&nbsp;</td>
            </tr>
            <tr>
                <td colspan="3" class="t-left">Noclegi wg rachunków</td>
                <td>&nbsp;</td>
            </tr>
            <tr>
                <td colspan="2" rowspan="2">
                    <br>
                    <div class="columns">
                        <div class="column">
                            <small>
                                <span data-property="date_start_plus_two_days">test</span>
                                <br>
                                data</small>
                        </div>
                        <div class="column">
                            <small>
                                . . . . . .
                                <br>
                                podpis</small>
                        </div>
                    </div>
                </td>
                <td colspan="2" rowspan="2">
                    <br>
                    <div class="columns">
                        <div class="column">
                            <small>
                                <span data-property="date_start_plus_two_days">test</span>
                                <br>
                                data</small>
                        </div>
                        <div class="column">
                            <small>
                                . . . . . .
                                <br>
                                podpis</small>
                        </div>
                    </div>
                </td>
                <td colspan="3" class="t-left">Noclegi – ryczałt</td>
                <td>&nbsp;</td>
            </tr>
            <tr>
                <td colspan="3" class="t-left">Inne wydatki wg załączników</td>
                <td>&nbsp;</td>
            </tr>
            <tr>
                <td colspan="4" rowspan="4">
                    <p class="t-left">Zatwierdzono na 0.00 euro
                        <br>
                        <br>
                        słownie . . . . . . . . . . . . . . . . . . . . . . . . . . . .
                        <br>
                        <br>
                        . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .
                        <br>
                        <br>
                        <br>
                        <br>
                    </p>
                    <div class="columns">
                        <div class="column">
                            <span data-property="date_start_plus_two_days">test</span>
                            <br>
                            <small>data</small>
                        </div>
                        <div class="column">
                            . . . . . . . . . . . . . . . . . .
                            <br>
                            <small>podpisy zatwierdzających</small>
                        </div>
                    </div>
                    <br>
                    <br>
                    <p class="t-left">Kwituję odbiór 0.00 euro
                        <br>
                        <br>
                        słownie . . . . . . . . . . . . . . . . . . . . . . . . . . . .
                        <br>
                        <br>
                        . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .
                        <br>
                        <br>
                        <br>
                        <br>
                    </p>
                    <div class="columns">
                        <div class="column">
                            <span data-property="date_start_plus_two_days">test</span>
                            <br>
                            <small>data</small>
                        </div>
                        <div class="column">
                            . . . . . . . . . . . . . . . . . .
                            <br>
                            <small>podpisy</small>
                        </div>
                    </div>
                    <br>
                    <br>
                </td>
                <td colspan="3">
                    <br>
                    <br>
                    <br>
                    <p class="t-right">Ogółem</p>
                    <br>
                    <p class="t-left">Słownie złotych:</p>
                    <br>
                    <br>
                    <br>
                    <br>
                    <br>
                    <br>
                    <br>
                </td>
                <td>&nbsp;</td>
            </tr>
            <tr>
                <td rowspan="2">
                    Załączam
                    <br>
                    <br>
                    . . . . . . . . . . . .
                    <br>
                    <small>dowodów</small>
                </td>
                <td colspan="2" class="t-left">Pobrano zaliczkę</td>
                <td>&nbsp;</td>
            </tr>
            <tr>
                <td colspan="2" class="t-left">Do wypłaty - zwrotu</td>
                <td>&nbsp;</td>
            </tr>
            <tr>
                <td colspan="4">
                    <p class="t-left">
                        Niniejszy rachunek przedkładam
                    </p>
                    <div class="columns">
                        <div class="column">
                            <span data-property="date_start_plus_two_days">test</span>
                            <br>
                            <small>data</small>
                        </div>
                        <div class="column">
                            . . . . . . . . .
                            <br>
                            <small>podpisy</small>
                        </div>
                    </div>
                    <br>
                </td>
            </tr>
        </table>
    </div>
    <br>
    <p>
        Zaliczkę w kwocie 0.00 euro . . . . . . . . . . . . . . słownie . . . . . . . . . . . . . . . . . . . . . . . .
        . . . . . . . . . . . . . . . . . . . . . . . otrzymałem i zobowiązuję się rozliczyć z niej w terminie . . . .
        dni po zakończeniu podróży, upoważniając równocześnie zakład pracy do potrącenia kwoty nierozliczonej zaliczki z
        najbliższej wypłaty wynagrodzenia.
    </p>
    <div class="columns t-center">
        <div class="column">
            <b><big><span data-property="full_name">full_name</span></big></b>
            <br>
            <small>imię i nazwisko delegowanego</small>
        </div>
        <div class="column">
            . . . . . . . . . . . . . . . . . . . . . . . . . . .
            <br>
            <small>data i podpis delegowanego</small>
        </div>
    </div>
    <p>
        <small>
            *) Wymienić środek lokomocji, klasę, rodzaj biletu (bezpłatny, ulgowy, normalny). W podróżach przy użyciu
            własnych środków lokomocji (samochód, motocykl, motorower) podać również ilość km i stawkę za 1 km.
        </small>
    </p>
    <div class="special_text">
        <small>Źródło: Wydawnictwo Podatkowe GOFIN sp. z o.o. - www.Druki.Gofin.pl</small>
    </div>
    <div class="page_number_text">
        str. 2
    </div>
</div>
</body>

</html>