<!DOCTYPE html>
<html lang="pl">

<head>
    <meta charset="UTF-8">
    <title>ZUS Form</title>
    <style>
        /*
            Common styles
        */

        :root {
            --color-border: grey;
        }

        .square {
            width: 25pt;
            height: 25pt;
            text-align: center;
            box-sizing: border-box;
            line-height: 12pt;
        }

        .hr {
            unicode-bidi: isolate;
            overflow: hidden;
            border-style: solid;
            border-width: 1pt;
            border-color: var(--color-border);
            border-top: none;
        }

        .text-bold-uppercase {
            font-weight: bold;
            text-transform: uppercase;
        }


        html {
            font-size: 13pt;
            font-style: normal;
            font-weight: normal;
            visibility: visible;
        }

        body {
            font-family: Arial, sans-serif;
            margin: 0;
        }

        .page {
            width: 793.626667pt;
            height: 1122.666667pt;
            padding: 45pt 60pt;
            box-sizing: border-box;
            position: relative;
            /*            page-break-after: always;*/
            page-break-inside: avoid;
        }

        .t-center {
            text-align: center;
        }

        .t-left {
            text-align: left;
        }

        .t-right {
            text-align: right;
        }

        .columns {
            display: flex;
        }

        .columns .column {
            flex: 1;
        }

        .special-text {
            text-align: center;
            position: absolute;
            width: 100%;
            left: 0;
            bottom: 0;
            margin-bottom: 30pt;
        }

        .page-number-text {
            text-align: right;
            position: absolute;
            width: 100%;
            left: 0;
            bottom: 0;
            margin-bottom: 30pt;
            padding-right: 60pt;
            box-sizing: border-box;
        }

        .form .input-row {
            display: flex;
            align-items: center;
        }

        .form .input-row :is(.label, .field) {
            margin: 3pt;
            box-sizing: border-box;
        }

        .form .input-row .label {
            text-align: right;
        }

        .text-input {
            padding: 6pt;
            border: 1pt solid var(--color-border);
        }

        .textarea-input {
            padding: 6pt;
            border: 1pt solid var(--color-border);
            height: 80pt;
        }

        .text-input-group {
            display: flex;
        }

        .text-input-group > div {
            padding: 6pt;
            border: 1pt solid var(--color-border);
        }

        .f-1 {
            flex: 1 !important;
        }

        .f-2 {
            flex: 2 !important;
        }

        .f-3 {
            flex: 3 !important;
        }

        .indent {
            margin-left: 13pt;
        }

        .horizontal {
            display: flex;
            align-items: center;
        }

        .text-input-group.date *:nth-child(1):after {
            content: "dd";
            position: absolute;
            margin-top: 20pt;
            font-weight: 800;
        }

        .text-input-group.date *:nth-child(2):after {
            content: "/";
            position: absolute;
            margin-top: 20pt;
            margin-left: 5pt;
            font-weight: 800;
        }

        .text-input-group.date *:nth-child(3):after {
            content: "mm";
            position: absolute;
            margin-top: 20pt;
            margin-left: -3pt;
            font-weight: 800;
        }

        .text-input-group.date *:nth-child(4):after {
            content: "/";
            position: absolute;
            margin-top: 20pt;
            margin-left: 5pt;
            font-weight: 800;
        }

        .text-input-group.date *:nth-child(6):after {
            content: "rrrr";
            position: absolute;
            margin-top: 20pt;
            margin-left: -1pt;
            font-weight: 800;
        }

        .main-number {
            font-size: 30pt;
            font-weight: 800;
            position: absolute;
            right: 65pt;
            margin-top: 5pt;
        }

        .sign-place {
            margin-bottom: -30pt;
        }


        h1 {
            font-weight: 800;
            font-size: 16pt;
            text-align: center;
            line-height: 20pt;
            margin: 20pt 0 0 0;
        }

        :is(h2, h3) {
            font-weight: 800;
            font-size: 13pt;
            text-align: left;
            margin: 10pt 0 0 0;
            box-sizing: border-box;
        }

        h2 {
            border-bottom: 1pt solid var(--color-border);
        }


        p {
            margin-top: 6pt;
            margin-left: 0;
            margin-bottom: 0;
            margin-right: 0;
            line-height: 13pt;
        }

        small {
            letter-spacing: -0.3pt;
        }

        .mt-small {
            margin-top: 3pt;
        }

        /*
            Page1 styles
        */

        .page1 .block1 {
            border-bottom: 1pt solid var(--color-border);
        }

        .page1 .block1 img {
            width: 100%;
            display: block;
        }

        /*
            Page2 styles
        */

        .page2 .block1 {
            height: 60pt;
            border-bottom: 1pt solid var(--color-border);
        }

        /*
            Help styles
            remove if unnecessary
        */

        /*
                *[data-property] {
                    font-weight: 800;
                    color: red;
                }
        */
    </style>
</head>

<body>
<div class="page page1">
    <div class="main-number">US-54</div>
    <div class="block1">
        <img src="data:image/png;base64,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">
    </div>
    <br>
    <h1>
        INFORMACJA O LEGALNOŚCI ZAMIESZKANIA OBYWATELA PAŃSTWA TRZECIEGO
    </h1>
    <br>
    <h2>Instrukcja wypełniania</h2>
    <p>Wypełnij, jeżeli jesteś obywatelem państwa trzeciego i składasz wniosek o wydanie zaświadczenia A1.</p>
    <p>Informacja ma na celu uzyskanie danych o legalności zamieszkania. Przepisy unijne, które są podstawą do
        potwierdzenia
        zaświadczenia A1, do obywateli państw trzecich stosuje się tylko wtedy, gdy osoby te legalnie zamieszkują na
        terytorium
        państwa członkowskiego.</p>
    <p>
        1. Wypełnij WIELKIMI LITERAMI
        <br>
        2. Pola wyboru zaznacz znakiem <b>X</b>
        <br>
        3. Wypełnij kolorem czarnym lub niebieskim (nie ołówkiem)
    </p>
    <h2>Dane osoby</h2>
    <div class="form">
        <div class="input-row">
            <div class="f-2">
                <div class="label">PESEL</div>
            </div>
            <div class="f-3">
                <div class="field">
                    <div class="text-input-group" data-property="pesel">
                        <div class="square"></div>
                        <div class="square"></div>
                        <div class="square"></div>
                        <div class="square"></div>
                        <div class="square"></div>
                        <div class="square"></div>
                        <div class="square"></div>
                        <div class="square"></div>
                        <div class="square"></div>
                        <div class="square"></div>
                        <div class="square"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="input-row">
            <div class="f-2">
                <div class="label">Rodzaj, seria i numer dokumentu potwierdzającego tożsamość</div>
            </div>
            <div class="f-3">
                <div class="field">
                    <div class="text-input" data-property="passport">passport</div>
                    <small>Jeśli nie masz numeru PESEL, podaj serię i numer innego dokumentu</small>
                </div>
            </div>
        </div>
        <div class="input-row">
            <div class="f-2">
                <div class="label">Imię</div>
            </div>
            <div class="f-3">
                <div class="field">
                    <div class="text-input" data-property="name">name</div>
                </div>
            </div>
        </div>
        <div class="input-row">
            <div class="f-2">
                <div class="label">Nazwisko</div>
            </div>
            <div class="f-3">
                <div class="field">
                    <div class="text-input" data-property="surname">surname</div>
                </div>
            </div>
        </div>
    </div>
    <h2>Adres w państwie zamieszkania</h2>
    <div class="form">
        <div class="input-row">
            <div class="f-2">
                <div class="label">Ulica</div>
            </div>
            <div class="f-3">
                <div class="field">
                    <div class="text-input">&nbsp;</div>
                </div>
            </div>
        </div>
        <div class="input-row">
            <div class="f-2">
                <div class="label">Numer domu</div>
            </div>
            <div class="f-1">
                <div class="field">
                    <div class="text-input">&nbsp;</div>
                </div>
            </div>
            <div class="f-1">
                <div class="label">Numer lokalu</div>
            </div>
            <div class="f-1">
                <div class="field">
                    <div class="text-input">&nbsp;</div>
                </div>
            </div>
        </div>
        <div class="input-row">
            <div class="f-2">
                <div class="label">Kod pocztowy</div>
            </div>
            <div class="f-1">
                <div class="field">
                    <div class="text-input">&nbsp;</div>
                </div>
            </div>
            <div class="f-1">
                <div class="label">Miejscowość</div>
            </div>
            <div class="f-1">
                <div class="field">
                    <div class="text-input">&nbsp;</div>
                </div>
            </div>
        </div>
        <div class="input-row">
            <div class="f-2">
                <div class="label">Nazwa państwa</div>
            </div>
            <div class="f-3">
                <div class="field">
                    <div class="text-input">&nbsp;</div>
                </div>
            </div>
        </div>
    </div>
    <h2>Adres w państwie pobytu</h2>
    <div class="form">
        <div class="input-row">
            <div class="f-2">
                <div class="label">Ulica</div>
            </div>
            <div class="f-3">
                <div class="field">
                    <div class="text-input" data-property="street">street</div>
                </div>
            </div>
        </div>
        <div class="input-row">
            <div class="f-2">
                <div class="label">Numer domu</div>
            </div>
            <div class="f-1">
                <div class="field">
                    <div class="text-input" data-property="house">house</div>
                </div>
            </div>
            <div class="f-1">
                <div class="label">Numer lokalu</div>
            </div>
            <div class="f-1">
                <div class="field">
                    <div class="text-input">&nbsp;</div>
                </div>
            </div>
        </div>
        <div class="input-row">
            <div class="f-2">
                <div class="label">Kod pocztowy</div>
            </div>
            <div class="f-1">
                <div class="field">
                    <div class="text-input" data-property="postal_code">postal_code</div>
                </div>
            </div>
            <div class="f-1">
                <div class="label">Miejscowość</div>
            </div>
            <div class="f-1">
                <div class="field">
                    <div class="text-input" data-property="city">city</div>
                </div>
            </div>
        </div>
        <div class="input-row">
            <div class="f-2">
                <div class="label">Nazwa państwa</div>
            </div>
            <div class="f-3">
                <div class="field">
                    <div class="text-input" data-property="country">country</div>
                </div>
            </div>
        </div>
    </div>
    <h2>Dane o pobycie w Polsce</h2>
    <small>Wskaż okresy pobytu w Polsce, na podstawie uprawniających Cię do tego odpowiednich dokumentów</small>
    <br>
    <br>
    <div class="form">
        <div class="input-row">
            <div class="f-2">
                <div class="label">
                    W Polsce
                    <br>
                    przebywałem/ przebywałam/ przebywamw
                    <br>
                    następujących okresach
                    <br>
                    (dane za okres ostatnich 3 lat)
                </div>
            </div>
            <div class="f-3">
                <div class="field">
                    <div class="textarea-input">&nbsp;</div>
                </div>
            </div>
        </div>
    </div>
    <br>
    <div class="hr"></div>
    <div class="special-text"><small>Zakład Ubezpieczeń Społecznych w internecie – <b>www.zus.pl</b></small></div>
    <div class="page-number-text"><b>Strona 1 z 2</b></div>
</div>
<div class="page page2">
    <div class="main-number">US-54</div>
    <div class="block1"></div>
    <br>
    <h2>Dokument, który uprawnia Cię do pobytu w Polsce</h2>
    <small>Załącz kopię jednego z poniższych dokumentów</small>
    <br>
    <div class="horizontal">
        <div class=""><b>Zezwolenie na</b>: pobyt rezydenta długoterminowego UE</div>
        <div class="text-input  square indent text-bold-uppercase" data-property="eu_citizenship">&nbsp;</div>
        <div class="indent">pobyt stały</div>
        <div class="text-input square indent text-bold-uppercase" data-property="karta_pobytu">&nbsp;</div>
        <div class="indent">pobyt czasowy oraz pracę</div>
        <div class="text-input square text-bold-uppercase indent" data-property="visa">&nbsp;</div>
    </div>
    <p>Wiza Schengen lub wiza krajowa wydana w celu:</p>
    <div class="horizontal">
        <div class="text-input square">&nbsp;&nbsp;&nbsp;</div>
        <div class="indent"><small>wykonywania pracy, w okresie nieprzekraczającym 6 miesięcy w ciągu kolejnych 12
            miesięcy, na podstawie wpisanego do ewidencji oświadczenia o powierzeniu wykonywania pracy cudzoziemcowi –
            art. 60 ust. 1 pkt 5 ustawy o cudzoziemcach*</small></div>
    </div>
    <div class="horizontal mt-small">
        <div class="text-input square">&nbsp;&nbsp;&nbsp;</div>
        <div class="indent"><small>wykonywania pracy, o której mowa w art. 88 ust. 2 ustawy z dnia 20 kwietnia 2004 r. o
            promocji zatrudnienia i instytucjach rynku pracy, w okresie nieprzekraczającym 9 miesięcy w roku
            kalendarzowym – art. 60 ust. 1 pkt 5a ustawy o cudzoziemcach*</small></div>
    </div>
    <div class="horizontal mt-small">
        <div class="text-input square">&nbsp;&nbsp;&nbsp;</div>
        <div class="indent"><small>wykonywania pracy innej niż określona w art. 60 ust. 1 pkt 5 i 5a ustawy o
            cudzoziemcach* – art. 60 ust. 1 pkt 6 tej ustawy</small></div>
    </div>
    <div class="horizontal mt-small">
        <div class="text-input square">&nbsp;&nbsp;&nbsp;</div>
        <div class="indent"><small>korzystania z uprawnień wynikających z posiadania Karty Polaka – art. 60 ust. 1 pkt
            20 ustawy o cudzoziemcach* wraz z kopią Karty Polaka</small></div>
    </div>
    <h2>Charakter i specyﬁ ka wykonywanej pracy najemnej lub pracy na własny rachunek</h2>
    <div class="horizontal mt-small">
        <div class="f-2 t-right">
            Rodzaj wykonywanej pracy
        </div>
        <div class="f-3 indent">
            <div class="text-input" data-property="work_type">work_type</div>
        </div>
    </div>
    <div class="horizontal">
        <div class="f-2 t-right">
            Okres obowiązywania umowy, na podstawie której wykonywana jest praca
        </div>
        <div class="f-3 indent horizontal">
            <div class="indent">od</div>
            <div class="text-input-group date indent" data-property="date_start">
                <div class="square">1</div>
                <div class="square">2</div>
                <div class="square"> 3</div>
                <div class="square">3</div>
                <div class="square">3</div>
                <div class="square">3</div>
                <div class="square">3</div>
                <div class="square">3</div>
            </div>
            <div class="indent">do</div>
            <div class="text-input-group date indent" data-property="date_end">
                <div class="square">1</div>
                <div class="square">2</div>
                <div class="square">3</div>
                <div class="square">3</div>
                <div class="square">3</div>
                <div class="square">3</div>
                <div class="square">3</div>
                <div class="square">3</div>
            </div>
        </div>
    </div>
    <h2>Obowiązek podatkowy</h2>
    <small>Załącz kopię „Zaświadczenia o miejscu zamieszkania lub siedzibie dla celów podatkowych (certyﬁ kat
        rezydencji)” wydanego przez organ podatkowy</small>
    <div class="horizontal mt-small">
        <div class="f-1 t-right">Czy w Polsce jesteś rezydentem podatkowym? (rezydent podatkowy – osoba, która ma
            miejsce zamieszkania dla celów podatkowych w Polsce)
        </div>
        <div class="text-input text-bold-uppercase square indent">X</div>
        <div class="indent">TAK</div>
        <div class="text-input square indent">&nbsp;&nbsp;</div>
        <div class="indent">NIE</div>
    </div>
    <h2>Działalność niezarobkowa</h2>
    <div class="horizontal mt-small">
        <div class="f-1 t-right">Czy w Polsce wykonujesz obecnie działalność niezarobkową (np. odbywasz studia, kurs
            języka polskiego, należysz do związku zawodowego, fundacji, stowarzyszenia w Polsce)
        </div>
        <div class="text-input square indent">&nbsp;&nbsp;</div>
        <div class="indent">TAK</div>
        <div class="text-input square text-bold-uppercase indent">X</div>
        <div class="indent">NIE</div>
    </div>
    <div class="horizontal mt-small">
        <div class="f-2 t-right">
            Jeśli TAK, określ jej rodzajInne okoliczności
        </div>
        <div class="f-3 indent">
            <div class="text-input">&nbsp;</div>
        </div>
    </div>
    <h2>Inne okoliczności</h2>
    <div class="horizontal mt-small">
        <div class="f-2 t-right">
            Opisz inne okoliczności, które uważasz za istotne dla sprawy (np. czy masz rodzinę w Polsce, masz zamiar ją
            założyć/sprowadzić, masz zamiar osiedlić się w Polsce, masz polskie pochodzenie, wykonujesz zawód, który ma
            charakter deﬁ cytowy na lokalnym rynku pracy)
        </div>
        <div class="f-3 indent">
            <div class="textarea-input">&nbsp;</div>
        </div>
    </div>
    <h2>Załączniki</h2>
    <div class="horizontal mt-small">
        <div>Załączam</div>
        <div class="text-input square indent" data-property="docs_number">2</div>
        <div class="indent">dokumentów</div>
    </div>
    <div class="hr mt-small"></div>
    <br>
    <br>
    <br>
    <div class="horizontal">
        <div>Data</div>
        <div class="text-input-group date indent" data-property="date_start">
            <div class="square">1</div>
            <div class="square">2</div>
            <div class="square">3</div>
            <div class="square">3</div>
            <div class="square">3</div>
            <div class="square">3</div>
            <div class="square">3</div>
            <div class="square">3</div>
        </div>
        <div class="f-1 indent t-center sign-place">
            <div class="hr"></div>
            Czytelny podpis
        </div>
    </div>
    <br>
    <p><small>* Ustawa z dnia 12 grudnia 2013 r. o cudzoziemcach (Dz.U. z 2018 r. poz. 2094, z późn. zm.)</small></p>
    <div class="hr"></div>
    <p>Informacje, o których mowa w art. 13 ust. 1 i 2 Rozporządzenia Parlamentu Europejskiego i Rady (UE) 2016/679 z
        dnia 27 kwietnia 2016 r. w sprawie ochrony osób fizycznych w związku z przetwarzaniem danych osobowych i w
        sprawie swobodnego przepływu takich danych oraz uchylenia dyrektywy 95/46/WE (RODO), są dostępne w centrali lub
        terenowych jednostkach organizacyjnych ZUS oraz na stronie internetowej ZUS pod adresem:
        http://bip.zus.pl/rodo/rodo-klauzule-informacyjne</p>
    <div class="hr"></div>
    <div class="special-text"><small>Zakład Ubezpieczeń Społecznych w internecie – <b>www.zus.pl</b></small></div>
    <div class="page-number-text"><b>Strona 2 z 2</b></div>
</div>
</body>

</html>