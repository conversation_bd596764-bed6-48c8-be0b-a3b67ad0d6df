import asyncio
from app.services.generate_documents import generate_documents
import tracemalloc
from telegram import Update
from telegram.ext import Application, CommandHandler, MessageHandler, filters, CallbackContext

TOKEN = '7583868546:AAGGJ0eSk2-bBOrNp7Ox3UQX1OxTugQMamY'


# Test comment
async def doc_main(update: Update):
    tracemalloc.start()
    await generate_documents(update)
    tracemalloc.stop()


async def start(update: Update, context: CallbackContext) -> None:
    allowed_nicknames = ['svyatoslav_gw', 'Sofa_GW', 'margaritagw', 'halynagw']

    user_nickname = update.message.from_user.username

    if user_nickname not in allowed_nicknames:  # Check if the user’s nickname is allowed
        await update.message.reply_text("Ви не маєте доступу до цієї функції.")
        return  # Stop execution if the nickname is not allowed

    await update.message.reply_text("Початок роботи...")
    await doc_main(update)
    await update.message.reply_text("Генерація документів завершена.")


def main():
    app = Application.builder().token(TOKEN).build()
    app.add_handler(CommandHandler("start", start))
    app.run_polling()


if __name__ == "__main__":
    main()
    # asyncio.run(doc_main())
